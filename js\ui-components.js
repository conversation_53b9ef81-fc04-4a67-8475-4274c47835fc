/**
 * UI 元件和模板管理模組
 * 負責處理使用者介面的渲染和互動
 */

const UIComponents = {
    // 模態框元素快取
    modals: {
        view: null,
        edit: null
    },

    // 初始化 UI 元件
    init() {
        this.cacheModalElements();
        this.setupEventListeners();
    },

    // 快取模態框元素
    cacheModalElements() {
        this.modals.view = {
            modal: document.getElementById('view-modal'),
            title: document.getElementById('view-modal-title'),
            body: document.getElementById('view-modal-body'),
            close: document.getElementById('view-modal-close')
        };

        this.modals.edit = {
            modal: document.getElementById('edit-modal'),
            title: document.getElementById('edit-modal-title'),
            close: document.getElementById('edit-modal-close'),
            form: document.getElementById('edit-form'),
            cancelBtn: document.getElementById('cancel-edit')
        };
    },

    // 設定基本事件監聽器
    setupEventListeners() {
        // 模態框關閉事件
        if (this.modals.view.close) {
            this.modals.view.close.addEventListener('click', () => this.hideModal('view'));
        }
        if (this.modals.edit.close) {
            this.modals.edit.close.addEventListener('click', () => this.hideModal('edit'));
        }
        if (this.modals.edit.cancelBtn) {
            this.modals.edit.cancelBtn.addEventListener('click', () => this.hideModal('edit'));
        }

        // 點擊模態框外部關閉
        if (this.modals.view.modal) {
            this.modals.view.modal.addEventListener('click', (e) => {
                if (e.target === this.modals.view.modal) {
                    this.hideModal('view');
                }
            });
        }
        if (this.modals.edit.modal) {
            this.modals.edit.modal.addEventListener('click', (e) => {
                if (e.target === this.modals.edit.modal) {
                    this.hideModal('edit');
                }
            });
        }
    },

    // 顯示模態框
    showModal(type) {
        const modal = this.modals[type];
        if (modal && modal.modal) {
            modal.modal.classList.remove('opacity-0', 'pointer-events-none');
            modal.modal.querySelector('.modal-content').classList.remove('scale-95');
            modal.modal.querySelector('.modal-content').classList.add('scale-100');
        }
    },

    // 隱藏模態框
    hideModal(type) {
        const modal = this.modals[type];
        if (modal && modal.modal) {
            modal.modal.classList.add('opacity-0', 'pointer-events-none');
            modal.modal.querySelector('.modal-content').classList.add('scale-95');
            modal.modal.querySelector('.modal-content').classList.remove('scale-100');
        }
    },

    // 格式化提示詞內容為 HTML
    formatPromptToHtml(text) {
        let html = '';
        const lines = text.split('\n').filter(line => line.trim() !== '');
        let inList = false;

        lines.forEach(line => {
            let processedLine = line.trim();

            // 樣式化佔位符 [text]
            processedLine = processedLine.replace(/\[(.*?)\]/g, 
                '<span class="bg-yellow-200 text-yellow-800 px-2 py-1 rounded-md font-mono text-sm">[$1]</span>');

            // 樣式化粗體文字 **text**
            processedLine = processedLine.replace(/\*\*(.*?)\*\*/g, 
                '<strong class="font-bold text-accent">$1</strong>');
            
            // 處理以冒號結尾的標題
            if (processedLine.endsWith('：')) {
                if (inList) {
                    html += '</ol>';
                    inList = false;
                }
                html += `<h4 class="text-lg font-semibold text-gray-900 mt-6 mb-3">${processedLine}</h4>`;
            }
            // 處理編號列表
            else if (/^\d+\.\s/.test(processedLine)) {
                if (!inList) {
                    html += '<ol class="list-decimal list-inside space-y-3 pl-2 mb-4">';
                    inList = true;
                }
                processedLine = processedLine.replace(/^\d+\.\s/, '');
                html += `<li class="text-gray-800">${processedLine}</li>`;
            } else {
                if (inList) {
                    html += '</ol>';
                    inList = false;
                }
                html += `<p class="text-gray-700 mb-4">${processedLine}</p>`;
            }
        });

        if (inList) {
            html += '</ol>';
        }

        return html;
    },

    // 生成開發提示詞卡片 HTML
    generateDevPromptCard(prompt) {
        // 判斷是否為 Font Awesome 圖示類別或 emoji
        const iconHtml = prompt.icon.includes('fa-') 
            ? `<i class="${prompt.icon} text-3xl"></i>`
            : `<span class="text-3xl">${prompt.icon}</span>`;
        
        return `
        <div class="bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow border border-accent-light">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-16 h-16 bg-accent-light rounded-lg flex items-center justify-center text-accent mr-4">
                        ${iconHtml}
                    </div>
                    <div>
                        <h4 class="text-xl font-bold text-accent">${this.escapeHtml(prompt.title)}</h4>
                        <span class="text-sm text-gray-500">${this.escapeHtml(prompt.category)}</span>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button class="edit-prompt-btn text-accent hover:text-accent p-2 rounded hover:bg-accent-light transition-colors" 
                            data-id="${prompt.id}" title="編輯">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="copy-prompt-btn text-accent hover:text-accent p-2 rounded hover:bg-accent-light transition-colors" 
                            data-id="${prompt.id}" title="複製">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="delete-prompt-btn text-accent hover:text-accent p-2 rounded hover:bg-accent-light transition-colors" 
                            data-id="${prompt.id}" title="刪除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <p class="text-gray-600 mb-4">${this.escapeHtml(prompt.description)}</p>
            <div class="text-xs text-gray-400">
                更新時間：${new Date(prompt.updatedAt).toLocaleDateString('zh-TW')}
            </div>
        </div>
        `;
    },

    // 渲染開發提示詞網格
    renderDevPromptsGrid(prompts) {
        const grid = document.getElementById('dev-prompts-grid');
        const emptyState = document.getElementById('empty-state');
        
        if (!grid || !emptyState) return;

        if (prompts.length === 0) {
            grid.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }

        emptyState.classList.add('hidden');
        grid.innerHTML = prompts.map(prompt => this.generateDevPromptCard(prompt)).join('');
    },

    // 更新圖示預覽
    updateIconPreview(iconValue) {
        const preview = document.getElementById('icon-preview');
        if (!preview) return;

        if (iconValue.includes('fa-')) {
            preview.innerHTML = `<i class="${iconValue}"></i>`;
        } else {
            preview.innerHTML = `<span>${iconValue}</span>`;
        }
    },

    // 填充編輯表單
    fillEditForm(prompt) {
        const form = this.modals.edit.form;
        if (!form) return;

        const fields = {
            'edit-title': prompt.title,
            'edit-description': prompt.description,
            'edit-icon': prompt.icon,
            'edit-category': prompt.category,
            'edit-content': prompt.content
        };

        Object.entries(fields).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value || '';
            }
        });

        this.updateIconPreview(prompt.icon);
    },

    // 清空編輯表單
    clearEditForm() {
        const form = this.modals.edit.form;
        if (!form) return;

        const defaultValues = {
            'edit-title': '',
            'edit-description': '',
            'edit-icon': 'fas fa-code',
            'edit-category': '',
            'edit-content': ''
        };

        Object.entries(defaultValues).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            }
        });

        this.updateIconPreview('fas fa-code');
    },

    // 從表單獲取資料
    getFormData() {
        const form = this.modals.edit.form;
        if (!form) return null;

        const formData = new FormData(form);
        return {
            title: formData.get('title'),
            description: formData.get('description'),
            icon: formData.get('icon') || 'fas fa-code',
            category: formData.get('category'),
            content: formData.get('content')
        };
    },

    // HTML 轉義
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // 複製文字到剪貼簿
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                await navigator.clipboard.writeText(text);
                alert('提示詞內容已複製到剪貼簿！');
            } else {
                throw new Error('瀏覽器不支援剪貼簿 API');
            }
        } catch (error) {
            console.error('複製失敗:', error);
            alert('複製提示詞失敗，請檢查瀏覽器主控台獲取更多資訊。');
        }
    },

    // 顯示確認對話框
    confirm(message) {
        return window.confirm(message);
    },

    // 顯示警告訊息
    alert(message) {
        window.alert(message);
    }
};

// 導出模組（支援不同的模組系統）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIComponents;
} else if (typeof window !== 'undefined') {
    window.UIComponents = UIComponents;
}
