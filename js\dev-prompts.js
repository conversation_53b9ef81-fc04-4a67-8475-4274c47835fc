/**
 * 開發提示詞管理模組
 * 負責管理程式開發相關的提示詞範本
 */

const DevPrompts = {
    // 本地儲存的提示詞資料
    prompts: {},
    
    // 當前編輯的提示詞 ID
    currentEditId: null,

    // 預設提示詞範本
    defaultPrompts: {
        'default_1': {
            id: 'default_1',
            title: 'React 元件開發助手',
            description: '協助建立 React 功能元件和 Hook',
            icon: 'fab fa-react',
            category: '前端開發',
            content: `你是一位 React 專家，專精於現代 React 開發模式。

請協助我：
1. **元件設計**：根據需求設計合適的元件結構
2. **Hook 使用**：選擇和實作適當的 React Hook
3. **效能優化**：提供效能優化建議
4. **最佳實務**：遵循 React 最佳實務和設計模式

請提供清晰、可維護的程式碼，並包含適當的註解說明。`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        },
        'default_2': {
            id: 'default_2',
            title: '程式碼審查助手',
            description: '協助進行程式碼審查和品質檢查',
            icon: 'fas fa-code-branch',
            category: '其他',
            content: `你是一位經驗豐富的程式碼審查專家。

請協助我審查以下程式碼：
[請貼上要審查的程式碼]

請從以下角度進行分析：
1. **程式碼品質**：可讀性、可維護性、一致性
2. **效能考量**：潛在的效能問題和優化建議
3. **安全性**：安全漏洞和風險評估
4. **最佳實務**：是否遵循業界最佳實務
5. **改進建議**：具體的改進方案和重構建議

請提供建設性的回饋和具體的改進建議。`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    },

    // 核心專家身份設定
    coreIdentity: {
        title: "核心專家身份設定",
        description: "這是與 AI 程式開發助手互動的基石，建議在每次對話開始或處理一系列相關開發任務時，先使用此提示為模型建立穩固的技術基礎。",
        content: `你現在是一位經驗豐富、技術精湛的「全端程式開發專家」，專精於現代軟體開發技術與最佳實務。你熟練掌握前端技術（React、Vue、Angular、JavaScript、TypeScript、HTML、CSS）、後端技術（Node.js、Python、Java、C#、Go）、資料庫設計（SQL、NoSQL）、雲端服務（AWS、Azure、GCP）、DevOps 工具鏈，並對軟體架構設計、效能優化、安全性實作有深入理解。

你的任務是精準理解我的開發需求、技術問題，並提供完整、詳盡、符合業界標準的程式碼解決方案、架構建議與最佳實務指導。在提供任何技術方案時，務必說明其技術原理、適用場景、潛在風險與替代方案。

請以清晰、有條理、邏輯嚴謹的方式呈現資訊，提供可執行的程式碼範例，並注重程式碼品質、可維護性與擴展性。若技術方案有不確定之處，請務必指出其限制性或需要進一步確認的部分。在所有回應中，請確保程式碼遵循最新的開發標準與安全實務。`
    },

    // 初始化：載入或建立預設提示詞
    init() {
        this.loadFromStorage();
        if (Object.keys(this.prompts).length === 0) {
            this.createDefaultPrompts();
        }
        this.updateLegacyIcons();
    },

    // 從 localStorage 載入資料
    loadFromStorage() {
        try {
            const stored = localStorage.getItem('devPrompts');
            this.prompts = stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.error('載入提示詞資料失敗:', error);
            this.prompts = {};
        }
    },

    // 儲存到 localStorage
    saveToStorage() {
        try {
            localStorage.setItem('devPrompts', JSON.stringify(this.prompts));
        } catch (error) {
            console.error('儲存提示詞資料失敗:', error);
        }
    },

    // 建立預設提示詞
    createDefaultPrompts() {
        this.prompts = { ...this.defaultPrompts };
        this.saveToStorage();
    },

    // 生成唯一 ID
    generateId() {
        return 'prompt_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    // 更新舊版圖示為 Font Awesome
    updateLegacyIcons() {
        const iconMap = {
            '⚛️': 'fab fa-react',
            '🔍': 'fas fa-search',
            '📝': 'fas fa-code',
            '💻': 'fas fa-laptop-code',
            '🗑️': 'fas fa-trash',
            '✏️': 'fas fa-edit',
            '📋': 'fas fa-copy'
        };
        
        let updated = false;
        Object.keys(this.prompts).forEach(id => {
            const prompt = this.prompts[id];
            if (iconMap[prompt.icon]) {
                this.prompts[id].icon = iconMap[prompt.icon];
                updated = true;
            }
        });
        
        if (updated) {
            this.saveToStorage();
        }
    },

    // 獲取所有提示詞
    getAllPrompts() {
        return this.prompts;
    },

    // 獲取特定提示詞
    getPrompt(id) {
        return this.prompts[id] || null;
    },

    // 新增提示詞
    createPrompt(promptData) {
        const id = this.generateId();
        const newPrompt = {
            id: id,
            ...promptData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.prompts[id] = newPrompt;
        this.saveToStorage();
        return newPrompt;
    },

    // 更新提示詞
    updatePrompt(id, updates) {
        if (!this.prompts[id]) {
            throw new Error(`提示詞 ${id} 不存在`);
        }
        
        this.prompts[id] = {
            ...this.prompts[id],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        
        this.saveToStorage();
        return this.prompts[id];
    },

    // 刪除提示詞
    deletePrompt(id) {
        if (!this.prompts[id]) {
            throw new Error(`提示詞 ${id} 不存在`);
        }
        
        delete this.prompts[id];
        this.saveToStorage();
        return true;
    },

    // 篩選提示詞
    filterPrompts(searchTerm = '', category = '') {
        return Object.values(this.prompts).filter(prompt => {
            const matchesSearch = !searchTerm || 
                prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                prompt.description.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesCategory = !category || prompt.category === category;
            return matchesSearch && matchesCategory;
        });
    },

    // 匯出資料
    exportData() {
        return JSON.stringify(this.prompts, null, 2);
    },

    // 匯入資料
    importData(jsonData) {
        try {
            const importedData = JSON.parse(jsonData);
            this.prompts = importedData;
            this.saveToStorage();
            return true;
        } catch (error) {
            console.error('匯入資料失敗:', error);
            return false;
        }
    },

    // 獲取核心身份設定
    getCoreIdentity() {
        return this.coreIdentity;
    },

    // 設定當前編輯 ID
    setCurrentEditId(id) {
        this.currentEditId = id;
    },

    // 獲取當前編輯 ID
    getCurrentEditId() {
        return this.currentEditId;
    },

    // 清除當前編輯 ID
    clearCurrentEditId() {
        this.currentEditId = null;
    }
};

// 導出模組（支援不同的模組系統）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DevPrompts;
} else if (typeof window !== 'undefined') {
    window.DevPrompts = DevPrompts;
}
