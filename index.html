<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能提示詞管理平台</title>
    
    <!-- 外部資源 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- 自訂樣式 -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-primary">

    <!-- 頁首和導航 -->
    <header id="header" class="bg-primary/80 backdrop-blur-md sticky top-0 z-50 shadow-sm">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <h1 class="text-xl md:text-2xl font-bold text-accent">智能提示詞管理平台</h1>
            <button id="mobile-menu-button" class="md:hidden text-3xl">☰</button>
        </nav>
        
        <!-- 分頁導航 -->
        <div class="container mx-auto px-6">
            <div class="flex border-b border-gray-200">
                <button id="legal-tab" class="tab-button px-6 py-3 font-medium text-gray-600 active">
                    <i class="fas fa-balance-scale mr-2"></i>法律工作
                </button>
                <button id="dev-tab" class="tab-button px-6 py-3 font-medium text-gray-600">
                    <i class="fas fa-code mr-2"></i>程式開發工作
                </button>
            </div>
        </div>
        
        <!-- 行動選單 -->
        <div id="mobile-menu" class="hidden md:hidden bg-primary/90">
            <button class="mobile-tab-button block text-center py-2 nav-link w-full" data-tab="legal">
                <i class="fas fa-balance-scale mr-2"></i>法律工作
            </button>
            <button class="mobile-tab-button block text-center py-2 nav-link w-full" data-tab="dev">
                <i class="fas fa-code mr-2"></i>程式開發工作
            </button>
        </div>
    </header>

    <!-- 主要內容區域 -->
    <main class="container mx-auto px-6 py-8">

        <!-- 法律工作分頁內容 -->
        <div id="legal-content" class="tab-content active">
            <section id="hero" class="text-center py-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-4">客製化提示詞：您的AI法律研究助手</h2>
                <p class="max-w-3xl mx-auto text-lg text-gray-700">
                    本儀表板提供專為臺灣執業律師設計的專家提示範本，涵蓋民事與工程案件的法律研究、文書撰寫等情境。透過這些客製化提示詞，您可以最大化大型語言模型（LLM）的效能，提升工作效率與精準度。
                </p>
            </section>

            <section id="core-identity" class="py-16">
                <h3 class="text-3xl font-bold text-center mb-8">核心專家身份設定</h3>
                <p class="text-center text-gray-700 max-w-4xl mx-auto mb-12">
                    這是與 LLM 互動的基石，建議在每次對話開始或處理一系列相關任務時，先使用此提示為模型建立穩固的專業基礎。
                </p>
                <div class="bg-secondary p-8 rounded-lg shadow-xl border border-accent-light max-w-4xl mx-auto">
                    <pre class="whitespace-pre-wrap font-mono text-sm md:text-base text-gray-800 bg-gray-50 p-6 rounded-md overflow-x-auto"><code id="legal-core-identity"></code></pre>
                </div>
            </section>

            <section id="scenarios" class="py-16">
                <h3 class="text-3xl font-bold text-center mb-8">應用情境範本</h3>
                <p class="text-center text-gray-700 max-w-4xl mx-auto mb-12">
                    點擊下方卡片，查看並複製針對不同執業情境客製化的詳細提示詞範本。
                </p>
                <div class="grid md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
                    <div class="scenario-card bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer border border-accent-light" data-scenario="情境一">
                        <h4 class="text-xl font-bold mb-2 flex items-center text-accent">
                            法條依據與案例查找 
                            <span class="ml-auto text-2xl text-accent"><i class="fas fa-search"></i></span>
                        </h4>
                        <p class="text-gray-600">快速釐清民事或工程案件的爭點，並查找相關判例或學說見解。</p>
                    </div>
                    <div class="scenario-card bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer border border-accent-light" data-scenario="情境二">
                        <h4 class="text-xl font-bold mb-2 flex items-center text-accent">
                            法律文書撰寫輔助 
                            <span class="ml-auto text-2xl text-accent"><i class="fas fa-pen-to-square"></i></span>
                        </h4>
                        <p class="text-gray-600">撰寫訴狀、書狀或法律意見書的草稿或提供撰寫方向。</p>
                    </div>
                    <div class="scenario-card bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer border border-accent-light" data-scenario="情境三">
                        <h4 class="text-xl font-bold mb-2 flex items-center text-accent">
                            工程法律特化研究與分析 
                            <span class="ml-auto text-2xl text-accent"><i class="fas fa-hard-hat"></i></span>
                        </h4>
                        <p class="text-gray-600">針對工程契約條款、施工爭議、鑑定意見等進行專業分析。</p>
                    </div>
                    <div class="scenario-card bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer border border-accent-light" data-scenario="情境四">
                        <h4 class="text-xl font-bold mb-2 flex items-center text-accent">
                            法律意見書草擬與風險評估 
                            <span class="ml-auto text-2xl text-accent"><i class="fas fa-balance-scale"></i></span>
                        </h4>
                        <p class="text-gray-600">為客戶提供特定法律問題的書面意見或風險評估。</p>
                    </div>
                </div>
            </section>
        </div>

        <!-- 程式開發工作分頁內容 -->
        <div id="dev-content" class="tab-content">
            <section id="dev-hero" class="text-center py-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-4">程式開發提示詞管理</h2>
                <p class="max-w-3xl mx-auto text-lg text-gray-700">
                    建立和管理您的程式開發專用提示詞範本。支援新增、編輯、刪除功能，讓您打造專屬的開發助手。
                </p>
            </section>

            <section id="dev-core-identity" class="py-16">
                <h3 class="text-3xl font-bold text-center mb-8">核心專家身份設定</h3>
                <p class="text-center text-gray-700 max-w-4xl mx-auto mb-12">
                    這是與 AI 程式開發助手互動的基石，建議在每次對話開始或處理一系列相關開發任務時，先使用此提示為模型建立穩固的技術基礎。
                </p>
                <div class="bg-secondary p-8 rounded-lg shadow-xl border border-accent-light max-w-4xl mx-auto">
                    <pre class="whitespace-pre-wrap font-mono text-sm md:text-base text-gray-800 bg-gray-50 p-6 rounded-md overflow-x-auto"><code id="dev-core-identity-content"></code></pre>
                </div>
            </section>

            <section class="py-16">
                <!-- 操作按鈕 -->
                <div class="flex flex-wrap gap-4 justify-center mb-8">
                    <button id="add-prompt-btn" class="btn-primary px-6 py-3 rounded-lg font-medium">
                        <i class="fas fa-plus mr-2"></i>新增提示詞
                    </button>
                    <button id="import-btn" class="btn-secondary px-6 py-3 rounded-lg font-medium">
                        <i class="fas fa-file-import mr-2"></i>匯入範本
                    </button>
                    <button id="export-btn" class="btn-secondary px-6 py-3 rounded-lg font-medium">
                        <i class="fas fa-file-export mr-2"></i>匯出範本
                    </button>
                </div>

                <!-- 搜尋和篩選 -->
                <div class="max-w-2xl mx-auto mb-8">
                    <div class="flex gap-4">
                        <input type="text" id="search-input" placeholder="搜尋提示詞..." 
                               class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:border-accent focus:outline-none">
                        <select id="category-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:border-accent focus:outline-none">
                            <option value="">所有分類</option>
                            <option value="前端開發">前端開發</option>
                            <option value="後端開發">後端開發</option>
                            <option value="資料庫">資料庫</option>
                            <option value="測試">測試</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                </div>

                <!-- 提示詞網格 -->
                <div id="dev-prompts-grid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
                    <!-- 開發提示詞將在此動態生成 -->
                </div>

                <!-- 空狀態 -->
                <div id="empty-state" class="text-center py-16 hidden">
                    <div class="text-6xl mb-4 text-gray-400">
                        <i class="fas fa-file-code"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-600">還沒有提示詞範本</h3>
                    <p class="text-gray-500 mb-6">點擊上方「新增提示詞」按鈕來建立您的第一個範本</p>
                    <button class="btn-primary px-6 py-3 rounded-lg font-medium" onclick="document.getElementById('add-prompt-btn').click()">
                        <i class="fas fa-plus mr-2"></i>開始建立
                    </button>
                </div>
            </section>
        </div>

    </main>

    <!-- 頁尾 -->
    <footer class="bg-gray-800 text-white text-center p-6">
        <p>&copy; 2025 智能提示詞管理平台。為您的工作效率量身打造。</p>
    </footer>

    <!-- 檢視模態框（法律提示詞） -->
    <div id="view-modal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 opacity-0 pointer-events-none z-50">
        <div class="modal-content bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto p-8 transform scale-95">
            <div class="flex justify-between items-center mb-4">
                <h3 id="view-modal-title" class="text-2xl font-bold text-accent"></h3>
                <button id="view-modal-close" class="text-3xl text-gray-500 hover:text-gray-800">&times;</button>
            </div>
            <div id="view-modal-body" class="text-gray-800 leading-relaxed">
                <!-- 提示詞內容將在此顯示 -->
            </div>
        </div>
    </div>

    <!-- 編輯模態框（開發提示詞） -->
    <div id="edit-modal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 opacity-0 pointer-events-none z-50">
        <div class="modal-content bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto p-8 transform scale-95">
            <div class="flex justify-between items-center mb-6">
                <h3 id="edit-modal-title" class="text-2xl font-bold text-accent">編輯提示詞</h3>
                <button id="edit-modal-close" class="text-3xl text-gray-500 hover:text-gray-800">&times;</button>
            </div>
            <form id="edit-form" class="edit-form space-y-6">
                <div>
                    <label for="edit-title" class="block text-sm font-medium text-gray-700 mb-2">標題</label>
                    <input type="text" id="edit-title" name="title" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                </div>
                <div>
                    <label for="edit-description" class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                    <textarea id="edit-description" name="description" rows="2" required
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg"></textarea>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="edit-icon" class="block text-sm font-medium text-gray-700 mb-2">
                            圖示 
                            <span class="text-xs text-gray-500">(Font Awesome 類別或 emoji)</span>
                        </label>
                        <div class="relative">
                            <input type="text" id="edit-icon" name="icon" placeholder="fas fa-code"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg pr-12">
                            <div id="icon-preview" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-lg text-accent">
                                <i class="fas fa-code"></i>
                            </div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">
                            範例：fas fa-code, fab fa-react, 📝
                        </div>
                        <!-- 快速圖示選擇 -->
                        <div class="flex flex-wrap gap-2 mt-2">
                            <button type="button" class="icon-quick-select px-2 py-1 text-sm border rounded hover:bg-gray-100" data-icon="fas fa-code">
                                <i class="fas fa-code"></i> 程式碼
                            </button>
                            <button type="button" class="icon-quick-select px-2 py-1 text-sm border rounded hover:bg-gray-100" data-icon="fab fa-react">
                                <i class="fab fa-react"></i> React
                            </button>
                            <button type="button" class="icon-quick-select px-2 py-1 text-sm border rounded hover:bg-gray-100" data-icon="fab fa-js">
                                <i class="fab fa-js"></i> JS
                            </button>
                            <button type="button" class="icon-quick-select px-2 py-1 text-sm border rounded hover:bg-gray-100" data-icon="fas fa-database">
                                <i class="fas fa-database"></i> 資料庫
                            </button>
                            <button type="button" class="icon-quick-select px-2 py-1 text-sm border rounded hover:bg-gray-100" data-icon="fas fa-bug">
                                <i class="fas fa-bug"></i> 除錯
                            </button>
                            <button type="button" class="icon-quick-select px-2 py-1 text-sm border rounded hover:bg-gray-100" data-icon="fas fa-cog">
                                <i class="fas fa-cog"></i> 設定
                            </button>
                        </div>
                    </div>
                    <div>
                        <label for="edit-category" class="block text-sm font-medium text-gray-700 mb-2">分類</label>
                        <select id="edit-category" name="category" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                            <option value="">選擇分類</option>
                            <option value="前端開發">前端開發</option>
                            <option value="後端開發">後端開發</option>
                            <option value="資料庫">資料庫</option>
                            <option value="測試">測試</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label for="edit-content" class="block text-sm font-medium text-gray-700 mb-2">提示詞內容</label>
                    <textarea id="edit-content" name="content" rows="12" required
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg font-mono text-sm"
                              placeholder="輸入您的提示詞內容..."></textarea>
                </div>
                <div class="flex justify-end space-x-4">
                    <button type="button" id="cancel-edit" class="btn-secondary px-6 py-2 rounded-lg">
                        取消
                    </button>
                    <button type="submit" class="btn-primary px-6 py-2 rounded-lg">
                        儲存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript 模組 -->
    <script src="js/legal-prompts.js"></script>
    <script src="js/dev-prompts.js"></script>
    <script src="js/ui-components.js"></script>
    <script src="js/app.js"></script>

    <!-- 初始化核心身份內容 -->
    <script>
        // 確保所有模組載入完成後再初始化
        function initializeCoreIdentities() {
            console.log('初始化核心身份設定...');

            // 載入法律核心身份
            if (typeof LegalPrompts !== 'undefined') {
                const legalCoreElement = document.getElementById('legal-core-identity');
                if (legalCoreElement) {
                    const legalCore = LegalPrompts.getCoreIdentity();
                    legalCoreElement.textContent = legalCore.content;
                    console.log('法律核心身份載入成功');
                } else {
                    console.error('找不到法律核心身份元素');
                }
            } else {
                console.error('LegalPrompts 模組未載入');
            }

            // 載入開發核心身份
            if (typeof DevPrompts !== 'undefined') {
                const devCoreElement = document.getElementById('dev-core-identity-content');
                if (devCoreElement) {
                    const devCore = DevPrompts.getCoreIdentity();
                    devCoreElement.textContent = devCore.content;
                    console.log('開發核心身份載入成功');
                } else {
                    console.error('找不到開發核心身份元素');
                }
            } else {
                console.error('DevPrompts 模組未載入');
            }
        }

        // 當 DOM 載入完成時初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 延遲一點確保所有模組都載入完成
            setTimeout(initializeCoreIdentities, 100);
        });
    </script>

</body>
</html>
