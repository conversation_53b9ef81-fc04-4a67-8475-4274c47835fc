/**
 * 主要應用邏輯
 * 協調各個模組並處理應用程式的整體流程
 */

const App = {
    // 應用狀態
    state: {
        currentTab: 'legal',
        searchTerm: '',
        categoryFilter: ''
    },

    // 初始化應用程式
    init() {
        this.initModules();
        this.setupTabSystem();
        this.setupDevWorkEvents();
        this.setupLegalWorkEvents();
        this.setupMobileMenu();
        this.setupSmoothScroll();
        
        // 載入初始資料
        this.loadInitialData();
    },

    // 初始化各個模組
    initModules() {
        if (typeof UIComponents !== 'undefined') {
            UIComponents.init();
        }
        if (typeof DevPrompts !== 'undefined') {
            DevPrompts.init();
        }
    },

    // 設定分頁系統
    setupTabSystem() {
        const legalTab = document.getElementById('legal-tab');
        const devTab = document.getElementById('dev-tab');
        const legalContent = document.getElementById('legal-content');
        const devContent = document.getElementById('dev-content');
        const mobileTabButtons = document.querySelectorAll('.mobile-tab-button');

        // 分頁切換函數
        const switchTab = (tabName) => {
            // 更新分頁按鈕狀態
            [legalTab, devTab].forEach(tab => tab && tab.classList.remove('active'));
            [legalContent, devContent].forEach(content => content && content.classList.remove('active'));
            
            if (tabName === 'legal') {
                legalTab && legalTab.classList.add('active');
                legalContent && legalContent.classList.add('active');
            } else {
                devTab && devTab.classList.add('active');
                devContent && devContent.classList.add('active');
            }
            
            this.state.currentTab = tabName;
            
            // 關閉行動選單
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu && mobileMenu.classList.add('hidden');
        };

        // 分頁事件監聽器
        legalTab && legalTab.addEventListener('click', () => switchTab('legal'));
        devTab && devTab.addEventListener('click', () => switchTab('dev'));
        
        mobileTabButtons.forEach(btn => {
            btn.addEventListener('click', () => switchTab(btn.dataset.tab));
        });
    },

    // 設定程式開發工作相關事件
    setupDevWorkEvents() {
        // 新增提示詞按鈕
        const addBtn = document.getElementById('add-prompt-btn');
        addBtn && addBtn.addEventListener('click', () => this.handleAddPrompt());

        // 匯出按鈕
        const exportBtn = document.getElementById('export-btn');
        exportBtn && exportBtn.addEventListener('click', () => this.handleExport());

        // 匯入按鈕
        const importBtn = document.getElementById('import-btn');
        importBtn && importBtn.addEventListener('click', () => this.handleImport());

        // 搜尋和篩選
        const searchInput = document.getElementById('search-input');
        const categoryFilter = document.getElementById('category-filter');
        
        searchInput && searchInput.addEventListener('input', (e) => {
            this.state.searchTerm = e.target.value;
            this.renderDevPrompts();
        });
        
        categoryFilter && categoryFilter.addEventListener('change', (e) => {
            this.state.categoryFilter = e.target.value;
            this.renderDevPrompts();
        });

        // 編輯表單提交
        const editForm = document.getElementById('edit-form');
        editForm && editForm.addEventListener('submit', (e) => this.handleFormSubmit(e));

        // 圖示輸入預覽
        const iconInput = document.getElementById('edit-icon');
        iconInput && iconInput.addEventListener('input', (e) => {
            UIComponents.updateIconPreview(e.target.value);
        });

        // 快速圖示選擇
        document.querySelectorAll('.icon-quick-select').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const iconValue = btn.dataset.icon;
                iconInput && (iconInput.value = iconValue);
                UIComponents.updateIconPreview(iconValue);
            });
        });
    },

    // 設定法律工作相關事件
    setupLegalWorkEvents() {
        const scenarioCards = document.querySelectorAll('.scenario-card');
        
        scenarioCards.forEach(card => {
            card.addEventListener('click', () => {
                const scenarioKey = card.dataset.scenario;
                const template = LegalPrompts.getTemplate(scenarioKey);
                
                if (template) {
                    this.showLegalPrompt(template);
                }
            });
        });
    },

    // 設定行動選單
    setupMobileMenu() {
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuButton && mobileMenuButton.addEventListener('click', () => {
            mobileMenu && mobileMenu.classList.toggle('hidden');
        });
    },

    // 設定平滑滾動
    setupSmoothScroll() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = anchor.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
                
                // 關閉行動選單
                const mobileMenu = document.getElementById('mobile-menu');
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
            });
        });
    },

    // 載入初始資料
    loadInitialData() {
        this.renderDevPrompts();
    },

    // 渲染開發提示詞
    renderDevPrompts() {
        if (typeof DevPrompts === 'undefined' || typeof UIComponents === 'undefined') {
            return;
        }

        const filteredPrompts = DevPrompts.filterPrompts(
            this.state.searchTerm, 
            this.state.categoryFilter
        );
        
        UIComponents.renderDevPromptsGrid(filteredPrompts);
        this.bindDevPromptEvents();
    },

    // 綁定開發提示詞事件
    bindDevPromptEvents() {
        // 編輯按鈕
        document.querySelectorAll('.edit-prompt-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleEditPrompt(btn.dataset.id);
            });
        });

        // 複製按鈕
        document.querySelectorAll('.copy-prompt-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleCopyPrompt(btn.dataset.id);
            });
        });

        // 刪除按鈕
        document.querySelectorAll('.delete-prompt-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleDeletePrompt(btn.dataset.id);
            });
        });
    },

    // 處理新增提示詞
    handleAddPrompt() {
        DevPrompts.clearCurrentEditId();
        UIComponents.clearEditForm();
        UIComponents.modals.edit.title.textContent = '新增提示詞';
        UIComponents.showModal('edit');
    },

    // 處理編輯提示詞
    handleEditPrompt(id) {
        const prompt = DevPrompts.getPrompt(id);
        if (!prompt) return;

        DevPrompts.setCurrentEditId(id);
        UIComponents.fillEditForm(prompt);
        UIComponents.modals.edit.title.textContent = '編輯提示詞';
        UIComponents.showModal('edit');
    },

    // 處理複製提示詞
    handleCopyPrompt(id) {
        const prompt = DevPrompts.getPrompt(id);
        if (prompt) {
            UIComponents.copyToClipboard(prompt.content);
        }
    },

    // 處理刪除提示詞
    handleDeletePrompt(id) {
        const prompt = DevPrompts.getPrompt(id);
        if (!prompt) return;

        if (UIComponents.confirm(`確定要刪除「${prompt.title}」嗎？此操作無法復原。`)) {
            DevPrompts.deletePrompt(id);
            this.renderDevPrompts();
        }
    },

    // 處理表單提交
    handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = UIComponents.getFormData();
        if (!formData) return;

        const currentEditId = DevPrompts.getCurrentEditId();
        
        try {
            if (currentEditId) {
                // 更新現有提示詞
                DevPrompts.updatePrompt(currentEditId, formData);
            } else {
                // 建立新提示詞
                DevPrompts.createPrompt(formData);
            }
            
            this.renderDevPrompts();
            UIComponents.hideModal('edit');
            DevPrompts.clearCurrentEditId();
        } catch (error) {
            console.error('儲存提示詞失敗:', error);
            UIComponents.alert('儲存失敗：' + error.message);
        }
    },

    // 處理匯出
    handleExport() {
        const dataStr = DevPrompts.exportData();
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'dev-prompts-' + new Date().toISOString().split('T')[0] + '.json';
        link.click();
        URL.revokeObjectURL(url);
    },

    // 處理匯入
    handleImport() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    if (UIComponents.confirm('匯入資料將會覆蓋現有的提示詞，確定要繼續嗎？')) {
                        if (DevPrompts.importData(e.target.result)) {
                            this.renderDevPrompts();
                            UIComponents.alert('匯入成功！');
                        } else {
                            UIComponents.alert('匯入失敗：檔案格式不正確');
                        }
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    },

    // 顯示法律提示詞
    showLegalPrompt(template) {
        UIComponents.modals.view.title.textContent = template.title;
        UIComponents.modals.view.body.innerHTML = UIComponents.formatPromptToHtml(template.code);
        UIComponents.showModal('view');
        
        // 複製到剪貼簿
        UIComponents.copyToClipboard(template.code);
    }
};

// 當 DOM 載入完成時初始化應用程式
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});

// 導出模組（支援不同的模組系統）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = App;
} else if (typeof window !== 'undefined') {
    window.App = App;
}
