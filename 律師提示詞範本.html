<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能提示詞管理平台</title>
    <!-- Chosen Palette: Scholarly Warmth -->
    <!-- Application Structure Plan: A tabbed dashboard-style SPA with two main sections: Legal Work (static templates) and Development Work (editable templates). Features include tab navigation, modal dialogs for viewing/editing, and local storage for custom prompts. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;500;700&display=swap');
        body {
            font-family: 'Noto Sans TC', sans-serif;
            background-color: #F8F7F4; /* Light Cream */
            color: #333333; /* Dark Grey */
        }
        .bg-primary { background-color: #F8F7F4; }
        .bg-secondary { background-color: #FFFFFF; }
        .text-accent { color: #C87E6A; } /* Warm Terracotta */
        .border-accent { border-color: #C87E6A; }
        .bg-accent { background-color: #C87E6A; }
        .bg-accent-light { background-color: #DDC3B8; } /* Lighter Terracotta */
        .nav-link {
            transition: color 0.3s ease, border-bottom-color 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .nav-link:hover, .nav-link.active {
            color: #C87E6A;
            border-bottom-color: #C87E6A;
        }
        .modal {
            transition: opacity 0.3s ease-in-out;
        }
        .modal-content {
            transition: transform 0.3s ease-in-out;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px; /* Constrain width */
            margin-left: auto;
            margin-right: auto;
            height: 300px; /* Base height for chart */
            max-height: 400px; /* Max height to prevent excessive size */
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }

        /* Knowledge Graph Styling */
        .node {
            background-color: #DDC3B8;
            border: 1px solid #C87E6A;
            border-radius: 8px;
            padding: 8px 12px;
            text-align: center;
            font-weight: 500;
            position: relative;
            z-index: 10;
        }
        .node.core {
            font-weight: bold;
            background-color: #C87E6A;
            color: white;
            padding: 12px 18px;
            border-radius: 12px;
        }

        /* Tab System Styling */
        .tab-button {
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        .tab-button.active {
            color: #C87E6A;
            border-bottom-color: #C87E6A;
            background-color: rgba(200, 126, 106, 0.1);
        }
        .tab-button:hover:not(.active) {
            color: #C87E6A;
            background-color: rgba(200, 126, 106, 0.05);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        /* Edit Form Styling */
        .edit-form input, .edit-form textarea {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .edit-form input:focus, .edit-form textarea:focus {
            border-color: #C87E6A;
            box-shadow: 0 0 0 3px rgba(200, 126, 106, 0.1);
            outline: none;
        }

        /* Action Buttons */
        .btn-primary {
            background-color: #C87E6A;
            color: white;
            transition: background-color 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #B86B57;
        }
        .btn-secondary {
            background-color: #DDC3B8;
            color: #333333;
            transition: background-color 0.3s ease;
        }
        .btn-secondary:hover {
            background-color: #D0B5A8;
        }
        .btn-danger {
            background-color: #DC3545;
            color: white;
            transition: background-color 0.3s ease;
        }
        .btn-danger:hover {
            background-color: #C82333;
        }
    </style>
</head>
<body class="bg-primary">

    <header id="header" class="bg-primary/80 backdrop-blur-md sticky top-0 z-50 shadow-sm">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <h1 class="text-xl md:text-2xl font-bold text-accent">智能提示詞管理平台</h1>
            <button id="mobile-menu-button" class="md:hidden text-3xl">☰</button>
        </nav>

        <!-- Tab Navigation -->
        <div class="container mx-auto px-6">
            <div class="flex border-b border-gray-200">
                <button id="legal-tab" class="tab-button px-6 py-3 font-medium text-gray-600 active">
                    ⚖️ 法律工作
                </button>
                <button id="dev-tab" class="tab-button px-6 py-3 font-medium text-gray-600">
                    💻 程式開發工作
                </button>
            </div>
        </div>

        <div id="mobile-menu" class="hidden md:hidden bg-primary/90">
            <button class="mobile-tab-button block text-center py-2 nav-link w-full" data-tab="legal">⚖️ 法律工作</button>
            <button class="mobile-tab-button block text-center py-2 nav-link w-full" data-tab="dev">💻 程式開發工作</button>
        </div>
    </header>

    <main class="container mx-auto px-6 py-8">

        <!-- Legal Work Tab Content -->
        <div id="legal-content" class="tab-content active">
            <section id="hero" class="text-center py-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-4">客製化提示詞：您的AI法律研究助手</h2>
                <p class="max-w-3xl mx-auto text-lg text-gray-700">
                    本儀表板提供專為臺灣執業律師設計的專家提示範本，涵蓋民事與工程案件的法律研究、文書撰寫等情境。透過這些客製化提示詞，您可以最大化大型語言模型（LLM）的效能，提升工作效率與精準度。
                </p>
            </section>

        <section id="core-identity" class="py-16">
            <h3 class="text-3xl font-bold text-center mb-8">核心專家身份設定</h3>
            <p class="text-center text-gray-700 max-w-4xl mx-auto mb-12">
                這是與 LLM 互動的基石，建議在每次對話開始或處理一系列相關任務時，先使用此提示為模型建立穩固的專業基礎。
            </p>
            <div class="bg-secondary p-8 rounded-lg shadow-xl border border-accent-light max-w-4xl mx-auto">
                <pre class="whitespace-pre-wrap font-mono text-sm md:text-base text-gray-800 bg-gray-50 p-6 rounded-md overflow-x-auto"><code>你現在是一位經驗豐富、知識淵博的「臺灣法律研究與文書撰寫專家」，專門處理臺灣民事與工程法律案件。你精通《民法》、《民事訴訟法》、相關特別法（如《公寓大廈管理條例》、《消費者保護法》、《民事事件訴訟標的金額核定辦法》、《建築法》、工程採購法規等），並對最新的實務見解（包含最高法院、高等法院判決、司法院大法官解釋、工程會函釋）有深入理解。

你的任務是精準理解我的法律問題、撰寫需求，並提供完整、詳盡、符合臺灣繁體中文法律用語的分析、案例與法條依據。在提出任何論點時，務必說明其法源依據或實務見解來源。

請以清晰、有條理、邏輯嚴謹的方式呈現資訊，並注重法律專業的精確性。若資訊有不確定之處，請務必指出其侷限性或不確定性。在所有回應中，請確保語氣專業、嚴謹，並避免使用簡體中文或大陸地區用語。
</code></pre>
            </div>
        </section>

        <section id="scenarios" class="py-16">
            <h3 class="text-3xl font-bold text-center mb-8">應用情境範本</h3>
            <p class="text-center text-gray-700 max-w-4xl mx-auto mb-12">
                點擊下方卡片，查看並複製針對不同執業情境客製化的詳細提示詞範本。
            </p>
            <div class="grid md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
                <div class="scenario-card bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer border border-accent-light" data-scenario="情境一">
                    <h4 class="text-xl font-bold mb-2 flex items-center text-accent">法條依據與案例查找 <span class="ml-auto text-2xl">🔍</span></h4>
                    <p class="text-gray-600">快速釐清民事或工程案件的爭點，並查找相關判例或學說見解。</p>
                </div>
                <div class="scenario-card bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer border border-accent-light" data-scenario="情境二">
                    <h4 class="text-xl font-bold mb-2 flex items-center text-accent">法律文書撰寫輔助 <span class="ml-auto text-2xl">✍️</span></h4>
                    <p class="text-gray-600">撰寫訴狀、書狀或法律意見書的草稿或提供撰寫方向。</p>
                </div>
                <div class="scenario-card bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer border border-accent-light" data-scenario="情境三">
                    <h4 class="text-xl font-bold mb-2 flex items-center text-accent">工程法律特化研究與分析 <span class="ml-auto text-2xl">🏗️</span></h4>
                    <p class="text-gray-600">針對工程契約條款、施工爭議、鑑定意見等進行專業分析。</p>
                </div>
                <div class="scenario-card bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer border border-accent-light" data-scenario="情境四">
                    <h4 class="text-xl font-bold mb-2 flex items-center text-accent">法律意見書草擬與風險評估 <span class="ml-auto text-2xl">⚖️</span></h4>
                    <p class="text-gray-600">為客戶提供特定法律問題的書面意見或風險評估。</p>
                </div>
            </div>
        </section>
        </div>

        <!-- Development Work Tab Content -->
        <div id="dev-content" class="tab-content">
            <section class="py-16">
                <div class="text-center mb-12">
                    <h2 class="text-4xl md:text-5xl font-bold mb-4">程式開發提示詞管理</h2>
                    <p class="max-w-3xl mx-auto text-lg text-gray-700">
                        建立和管理您的程式開發專用提示詞範本。支援新增、編輯、刪除功能，讓您打造專屬的開發助手。
                    </p>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-4 justify-center mb-8">
                    <button id="add-prompt-btn" class="btn-primary px-6 py-3 rounded-lg font-medium">
                        ➕ 新增提示詞
                    </button>
                    <button id="import-btn" class="btn-secondary px-6 py-3 rounded-lg font-medium">
                        📥 匯入範本
                    </button>
                    <button id="export-btn" class="btn-secondary px-6 py-3 rounded-lg font-medium">
                        📤 匯出範本
                    </button>
                </div>

                <!-- Search and Filter -->
                <div class="max-w-2xl mx-auto mb-8">
                    <div class="flex gap-4">
                        <input type="text" id="search-input" placeholder="搜尋提示詞..."
                               class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:border-accent focus:outline-none">
                        <select id="category-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:border-accent focus:outline-none">
                            <option value="">所有分類</option>
                            <option value="前端開發">前端開發</option>
                            <option value="後端開發">後端開發</option>
                            <option value="資料庫">資料庫</option>
                            <option value="測試">測試</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                </div>

                <!-- Prompts Grid -->
                <div id="dev-prompts-grid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
                    <!-- Development prompts will be dynamically inserted here -->
                </div>

                <!-- Empty State -->
                <div id="empty-state" class="text-center py-16 hidden">
                    <div class="text-6xl mb-4">📝</div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-600">還沒有提示詞範本</h3>
                    <p class="text-gray-500 mb-6">點擊上方「新增提示詞」按鈕來建立您的第一個範本</p>
                    <button class="btn-primary px-6 py-3 rounded-lg font-medium" onclick="document.getElementById('add-prompt-btn').click()">
                        開始建立
                    </button>
                </div>
            </section>
        </div>

    </main>

    <footer class="bg-gray-800 text-white text-center p-6">
        <p>&copy; 2025 智能提示詞管理平台。為您的工作效率量身打造。</p>
    </footer>

    <!-- View Modal (for legal prompts) -->
    <div id="view-modal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 opacity-0 pointer-events-none z-50">
        <div id="view-modal-content-container" class="modal-content bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto p-8 transform scale-95">
            <div class="flex justify-between items-center mb-4">
                <h3 id="view-modal-title" class="text-2xl font-bold text-accent"></h3>
                <button id="view-modal-close" class="text-3xl text-gray-500 hover:text-gray-800">&times;</button>
            </div>
            <div id="view-modal-body" class="text-gray-800 leading-relaxed">
                <!-- Prompt content will be injected here by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Edit Modal (for development prompts) -->
    <div id="edit-modal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 opacity-0 pointer-events-none z-50">
        <div class="modal-content bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto p-8 transform scale-95">
            <div class="flex justify-between items-center mb-6">
                <h3 id="edit-modal-title" class="text-2xl font-bold text-accent">編輯提示詞</h3>
                <button id="edit-modal-close" class="text-3xl text-gray-500 hover:text-gray-800">&times;</button>
            </div>
            <form id="edit-form" class="edit-form space-y-6">
                <div>
                    <label for="edit-title" class="block text-sm font-medium text-gray-700 mb-2">標題</label>
                    <input type="text" id="edit-title" name="title" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                </div>
                <div>
                    <label for="edit-description" class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                    <textarea id="edit-description" name="description" rows="2" required
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg"></textarea>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="edit-icon" class="block text-sm font-medium text-gray-700 mb-2">圖示 (emoji)</label>
                        <input type="text" id="edit-icon" name="icon" placeholder="📝"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label for="edit-category" class="block text-sm font-medium text-gray-700 mb-2">分類</label>
                        <select id="edit-category" name="category" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                            <option value="">選擇分類</option>
                            <option value="前端開發">前端開發</option>
                            <option value="後端開發">後端開發</option>
                            <option value="資料庫">資料庫</option>
                            <option value="測試">測試</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label for="edit-content" class="block text-sm font-medium text-gray-700 mb-2">提示詞內容</label>
                    <textarea id="edit-content" name="content" rows="12" required
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg font-mono text-sm"
                              placeholder="輸入您的提示詞內容..."></textarea>
                </div>
                <div class="flex justify-end space-x-4">
                    <button type="button" id="cancel-edit" class="btn-secondary px-6 py-2 rounded-lg">
                        取消
                    </button>
                    <button type="submit" class="btn-primary px-6 py-2 rounded-lg">
                        儲存
                    </button>
                </div>
            </form>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab System
    const legalTab = document.getElementById('legal-tab');
    const devTab = document.getElementById('dev-tab');
    const legalContent = document.getElementById('legal-content');
    const devContent = document.getElementById('dev-content');
    const mobileTabButtons = document.querySelectorAll('.mobile-tab-button');

    // Tab switching function
    function switchTab(tabName) {
        // Update tab buttons
        [legalTab, devTab].forEach(tab => tab.classList.remove('active'));
        [legalContent, devContent].forEach(content => content.classList.remove('active'));

        if (tabName === 'legal') {
            legalTab.classList.add('active');
            legalContent.classList.add('active');
        } else {
            devTab.classList.add('active');
            devContent.classList.add('active');
        }

        // Close mobile menu
        document.getElementById('mobile-menu').classList.add('hidden');
    }

    // Tab event listeners
    legalTab.addEventListener('click', () => switchTab('legal'));
    devTab.addEventListener('click', () => switchTab('dev'));
    mobileTabButtons.forEach(btn => {
        btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });

    // Legal prompts data (unchanged)
    const promptTemplates = {
        "情境一": {
            title: "情境一：法律爭點研究與案例查找",
            purpose: "快速釐清民事或工程案件的爭點，並查找相關判例或學說見解。",
            code: `身為我的「臺灣民事/工程法資深研究助理」，我現在有一個關於 [具體案件類型，例如：承攬瑕疵擔保責任、預售屋解約、公寓大廈共有部分修繕、民事訴訟法上之爭點整理效力、借名登記返還] 的法律爭點。

請你協助我：
1.  **釐清核心法律爭點：** 針對 [請在此簡述案情，例如：A 屋主主張 B 營造商施作之頂樓防水工程有滲漏情形，要求 B 負擔修復費用。] 歸納出主要的法律爭點。
2.  **搜尋相關法條：** 列出適用於這些爭點的臺灣相關法規條文（包括《民法》、《民事訴訟法》或其他相關特別法）。
3.  **查找最新實務見解：** 搜尋近五年內（請註明搜尋期間，例如：2020 年迄今）最高法院、高等法院關於此爭點的重要判決字號、裁判要旨或解釋文。如果能找到類型相似的實務見解，請優先提供。若為工程案件，請同時搜尋工程會相關函釋或行政法院判決。
4.  **提供學說觀點（若有必要）：** 若此爭點有學說上較為爭議或多元的觀點，請簡要說明。

請以條列式或段落分明的方式呈現，並註明所有引用來源。`
        },
        "情境二": {
            title: "情境二：法律文書撰寫輔助",
            purpose: "撰寫訴狀、書狀或法律意見書的草稿或提供撰寫方向。",
            code: `你現在是一位具備卓越中文法律寫作能力的「臺灣法律文書撰寫專家」。

我需要你協助我草擬一份關於 [具體文書類型，例如：起訴狀、準備書狀、答辯狀、催告函、存證信函、支付命令聲請狀] 的草稿，針對以下案情與我的主張。請確保內容符合臺灣法律文書的格式與用語習慣，語氣清晰且具說服力。

**案件資訊：**
* **當事人：** [原告/被告/發函方名稱] vs. [被告/原告/受函方名稱]
* **訴訟標的/爭議內容：** [請簡述訴訟標的，例如：承攬報酬給付、確認契約關係存在、返還不當得利、工程逾期罰款、損害賠償]
* **主要事實：** [詳細描述案件發生的時間、地點、人物、經過等，可分點條列。請替換敏感資訊，例如：將真實人名換為 A、B、C；公司名稱換為甲公司、乙公司。]
* **我方主張/請求：** [明確列出我方希望達成的法律目的或具體請求，例如：請求被告給付新台幣 X 元、請求確認契約關係不存在、請求排除侵害]
* **法條依據（已知部分，可選填）：** [若您已初步篩選法條，可在此提供，例如：民法第 XXX 條、第 YYY 條；公寓大廈管理條例第 ZZZ 條]
* **相關證據（可選填）：：** [若有證據清單，可在此簡述，例如：工程合約、估價單、驗收證明、通訊紀錄、鑑定報告]

**撰寫指示：**
1.  **結構：** 請遵循 [起訴狀/準備書狀/答辯狀] 的標準架構（例如：案號及股別、原被告資料、訴訟標的、訴之聲明、事實、理由、證據、管轄法院等）。
2.  **語氣：** [請選擇合適的語氣，例如：客觀嚴謹、主張明確、措辭溫和但堅定、據理力爭、威嚴且具警告性（催告函）]。
3.  **重點：** [請明確指出希望 LLM 在撰寫時強調的論點，例如：特別強調工程逾期造成之損害、證明對方未履行主要給付義務、針對特定證據進行有力論證]
4.  **字數/篇幅要求（可選填）：** [例如：約 800 字、僅提供各段落標題與簡要內容]

請以符合臺灣法律文書排版與標點符號習慣的格式呈現，並避免使用簡體中文或大陸地區用語。`
        },
        "情境三": {
            title: "情境三：工程法律特化研究與分析",
            purpose: "針對工程契約條款、施工爭議、鑑定意見等進行專業分析。",
            code: `你現在是一位專精於「臺灣工程法律」的頂尖顧問，具備法律與工程實務的雙重背景，並對工程實務流程、工程會相關函釋、工程契約慣例及鑑定報告解讀有深入見解。

我有一個關於 [具體工程爭議點，例如：估驗計價爭議、工期延宕責任歸屬、工程變更爭議、設計錯誤導致損害賠償、物價調整爭議、共同投標爭議] 的問題。

**案情摘要：** [請詳細描述工程案件背景與爭議點，包括契約內容、相關事件時間軸、涉及之圖說/變更單/鑑定報告、會議紀錄等。請替換敏感資訊，例如：將真實公司名稱換為甲承攬人、乙業主。]

**請你協助我：**
1.  **釐清爭點依據：** 針對此爭議點，說明可能適用的工程契約條款、相關法規命令（如《政府採購法》及其子法）或工程會函釋。
2.  **常見實務見解：** 查找相關法院判決或仲裁判斷，說明實務上對於此類爭議的認定標準或處理原則，並特別指出工程案件的特殊性。
3.  **鑑定意見分析（若有）：** 如果有鑑定報告，請你模擬分析報告中可能存在的偏頗、遺漏、邏輯不符之處、與我方主張矛盾之處，以及我方可以如何質疑或反駁鑑定意見。
4.  **風險評估與建議：** 根據上述分析，提供我方可能面臨的法律風險，並提出初步的法律策略建議，包括舉證責任的分配、爭議解決途徑（例如：調解、仲裁、訴訟）的考量。

請以工程師與律師皆能理解的專業語言進行分析，並在必要時引用相關條文、判例、函釋或工程專業術語。`
        },
        "情境四": {
            title: "情境四：法律意見書草擬與風險評估",
            purpose: "為客戶提供特定法律問題的書面意見或風險評估。",
            code: `你現在是一位以客觀、中立且具洞察力聞名的「臺灣法律意見撰寫專家」。

我需要你協助我草擬一份關於 [具體法律問題，例如：某投資方案的法律風險評估、某新商業模式的合法性分析、合約條款的權義評估、不動產交易風險分析、智慧財產權侵權風險評估] 的法律意見書。

**客戶需求背景：** [詳細說明客戶的需求、其業務模式、關切點等。請替換敏感資訊。]
**核心法律問題：** [明確指出客戶希望獲得法律意見的核心問題]
**已知資訊與文件：** [列出所有相關的合約、法規、新聞或其他文件。若有附件，可指示 LLM 模擬其內容或說明其重要性。請替換敏感資訊。]

**撰寫指示：**
1.  **結構：** 請依照標準法律意見書的架構（例如：案情摘要、問題、法規及實務見解、分析、結論與建議）。
2.  **分析深度：** 請深入分析可能涉及的法律風險、潛在責任、可行性，並考量不同的法律解釋可能性。
3.  **結論與建議：** 提供明確的法律結論，並針對客戶需求提出具體、可執行的法律建議，考量實務操作面。
4.  **語氣：** 保持專業、客觀、嚴謹，用詞精確，同時確保內容能讓非法律背景的客戶理解。
5.  **字數/篇幅（可選填）：** [例如：約 1500 字、提供詳細提綱及重點內容]

請確保所有分析皆符合臺灣現行法律規範與實務見解，並以客戶易於理解但仍保有專業水準的繁體中文呈現。`
        }
    };

    // Development prompts data (stored in localStorage)
    let devPrompts = JSON.parse(localStorage.getItem('devPrompts')) || {};
    let currentEditId = null;

    // Modal elements
    const viewModal = document.getElementById('view-modal');
    const viewModalTitle = document.getElementById('view-modal-title');
    const viewModalBody = document.getElementById('view-modal-body');
    const viewModalClose = document.getElementById('view-modal-close');

    const editModal = document.getElementById('edit-modal');
    const editModalTitle = document.getElementById('edit-modal-title');
    const editModalClose = document.getElementById('edit-modal-close');
    const editForm = document.getElementById('edit-form');

    const scenarioCards = document.querySelectorAll('.scenario-card');
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    // Mobile menu toggle
    mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
    });

    // Development prompts management
    function saveDevPrompts() {
        localStorage.setItem('devPrompts', JSON.stringify(devPrompts));
    }

    function generateId() {
        return 'prompt_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    function createDefaultPrompts() {
        if (Object.keys(devPrompts).length === 0) {
            devPrompts = {
                'default_1': {
                    id: 'default_1',
                    title: 'React 元件開發助手',
                    description: '協助建立 React 功能元件和 Hook',
                    icon: '⚛️',
                    category: '前端開發',
                    content: `你是一位 React 專家，專精於現代 React 開發模式。

請協助我：
1. **元件設計**：根據需求設計合適的元件結構
2. **Hook 使用**：選擇和實作適當的 React Hook
3. **效能優化**：提供效能優化建議
4. **最佳實務**：遵循 React 最佳實務和設計模式

請提供清晰、可維護的程式碼，並包含適當的註解說明。`,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                'default_2': {
                    id: 'default_2',
                    title: '程式碼審查助手',
                    description: '協助進行程式碼審查和品質檢查',
                    icon: '🔍',
                    category: '其他',
                    content: `你是一位經驗豐富的程式碼審查專家。

請協助我審查以下程式碼：
[請貼上要審查的程式碼]

請從以下角度進行分析：
1. **程式碼品質**：可讀性、可維護性、一致性
2. **效能考量**：潛在的效能問題和優化建議
3. **安全性**：安全漏洞和風險評估
4. **最佳實務**：是否遵循業界最佳實務
5. **改進建議**：具體的改進方案和重構建議

請提供建設性的回饋和具體的改進建議。`,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            };
            saveDevPrompts();
        }
    }

    function renderDevPrompts() {
        const grid = document.getElementById('dev-prompts-grid');
        const emptyState = document.getElementById('empty-state');
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const categoryFilter = document.getElementById('category-filter').value;

        const filteredPrompts = Object.values(devPrompts).filter(prompt => {
            const matchesSearch = prompt.title.toLowerCase().includes(searchTerm) ||
                                prompt.description.toLowerCase().includes(searchTerm);
            const matchesCategory = !categoryFilter || prompt.category === categoryFilter;
            return matchesSearch && matchesCategory;
        });

        if (filteredPrompts.length === 0) {
            grid.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }

        emptyState.classList.add('hidden');
        grid.innerHTML = filteredPrompts.map(prompt => `
            <div class="bg-secondary p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow border border-accent-light">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3">${prompt.icon}</span>
                        <div>
                            <h4 class="text-xl font-bold text-accent">${prompt.title}</h4>
                            <span class="text-sm text-gray-500">${prompt.category}</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="edit-prompt-btn text-blue-600 hover:text-blue-800" data-id="${prompt.id}" title="編輯">
                            ✏️
                        </button>
                        <button class="copy-prompt-btn text-green-600 hover:text-green-800" data-id="${prompt.id}" title="複製">
                            📋
                        </button>
                        <button class="delete-prompt-btn text-red-600 hover:text-red-800" data-id="${prompt.id}" title="刪除">
                            🗑️
                        </button>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">${prompt.description}</p>
                <div class="text-xs text-gray-400">
                    更新時間：${new Date(prompt.updatedAt).toLocaleDateString('zh-TW')}
                </div>
            </div>
        `).join('');

        // Add event listeners to action buttons
        document.querySelectorAll('.edit-prompt-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                editPrompt(btn.dataset.id);
            });
        });

        document.querySelectorAll('.copy-prompt-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                copyPrompt(btn.dataset.id);
            });
        });

        document.querySelectorAll('.delete-prompt-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                deletePrompt(btn.dataset.id);
            });
        });
    }

    function formatPromptToHtml(text) {
        let html = '';
        const lines = text.split('\n').filter(line => line.trim() !== '');

        let inList = false;

        lines.forEach(line => {
            let processedLine = line.trim();

            // Style placeholders like [text]
            processedLine = processedLine.replace(/\[(.*?)\]/g, '<span class="bg-yellow-200 text-yellow-800 px-2 py-1 rounded-md font-mono text-sm">[$1]</span>');

            // Style bold text like **text**
            processedLine = processedLine.replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold text-accent">$1</strong>');
            
            // Handle titles that end with a colon
            if (processedLine.endsWith('：')) {
                 if (inList) {
                    html += '</ol>';
                    inList = false;
                }
                 html += `<h4 class="text-lg font-semibold text-gray-900 mt-6 mb-3">${processedLine}</h4>`;
            }
            // Handle numbered lists
            else if (/^\d+\.\s/.test(processedLine)) {
                if (!inList) {
                    html += '<ol class="list-decimal list-inside space-y-3 pl-2 mb-4">';
                    inList = true;
                }
                processedLine = processedLine.replace(/^\d+\.\s/, '');
                html += `<li class="text-gray-800">${processedLine}</li>`;
            } else {
                if (inList) {
                    html += '</ol>';
                    inList = false;
                }
                html += `<p class="text-gray-700 mb-4">${processedLine}</p>`;
            }
        });

        if (inList) {
            html += '</ol>';
        }

        return html;
    }

    // Development prompt management functions
    function editPrompt(id) {
        const prompt = devPrompts[id];
        if (!prompt) return;

        currentEditId = id;
        editModalTitle.textContent = '編輯提示詞';

        document.getElementById('edit-title').value = prompt.title;
        document.getElementById('edit-description').value = prompt.description;
        document.getElementById('edit-icon').value = prompt.icon;
        document.getElementById('edit-category').value = prompt.category;
        document.getElementById('edit-content').value = prompt.content;

        showModal(editModal);
    }

    function copyPrompt(id) {
        const prompt = devPrompts[id];
        if (!prompt) return;

        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(prompt.content)
                .then(() => {
                    alert('提示詞內容已複製到剪貼簿！');
                })
                .catch(err => {
                    console.error('無法複製提示詞: ', err);
                    alert('複製提示詞失敗，請檢查瀏覽器主控台獲取更多資訊。');
                });
        } else {
            console.error('瀏覽器不支援剪貼簿 API。');
            alert('您的瀏覽器不支援自動複製功能。');
        }
    }

    function deletePrompt(id) {
        const prompt = devPrompts[id];
        if (!prompt) return;

        if (confirm(`確定要刪除「${prompt.title}」嗎？此操作無法復原。`)) {
            delete devPrompts[id];
            saveDevPrompts();
            renderDevPrompts();
        }
    }

    function addNewPrompt() {
        currentEditId = null;
        editModalTitle.textContent = '新增提示詞';

        document.getElementById('edit-title').value = '';
        document.getElementById('edit-description').value = '';
        document.getElementById('edit-icon').value = '📝';
        document.getElementById('edit-category').value = '';
        document.getElementById('edit-content').value = '';

        showModal(editModal);
    }

    // Legal scenario cards (unchanged functionality)
    scenarioCards.forEach(card => {
        card.addEventListener('click', function() {
            const scenarioKey = this.dataset.scenario;
            const data = promptTemplates[scenarioKey];

            // Display beautified prompt in modal
            viewModalTitle.textContent = data.title;
            viewModalBody.innerHTML = formatPromptToHtml(data.code);
            showModal(viewModal);

            // Copy raw prompt text to clipboard
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(data.code)
                    .then(() => {
                        alert('提示詞內容已複製到剪貼簿！');
                    })
                    .catch(err => {
                        console.error('無法複製提示詞: ', err);
                        alert('複製提示詞失敗，請檢查瀏覽器主控台獲取更多資訊。');
                    });
            } else {
                console.error('瀏覽器不支援剪貼簿 API。');
                alert('您的瀏覽器不支援自動複製功能。');
            }
        });
    });

    // Modal control functions
    function showModal(modal) {
        modal.classList.remove('opacity-0', 'pointer-events-none');
        modal.querySelector('.modal-content').classList.remove('scale-95');
        modal.querySelector('.modal-content').classList.add('scale-100');
    }

    function hideModal(modal) {
        modal.classList.add('opacity-0', 'pointer-events-none');
        modal.querySelector('.modal-content').classList.add('scale-95');
        modal.querySelector('.modal-content').classList.remove('scale-100');
    }

    // Modal event listeners
    viewModalClose.addEventListener('click', () => hideModal(viewModal));
    editModalClose.addEventListener('click', () => hideModal(editModal));
    document.getElementById('cancel-edit').addEventListener('click', () => hideModal(editModal));

    viewModal.addEventListener('click', function(event) {
        if (event.target === viewModal) {
            hideModal(viewModal);
        }
    });

    editModal.addEventListener('click', function(event) {
        if (event.target === editModal) {
            hideModal(editModal);
        }
    });

    // Form submission
    editForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(editForm);
        const promptData = {
            title: formData.get('title'),
            description: formData.get('description'),
            icon: formData.get('icon') || '📝',
            category: formData.get('category'),
            content: formData.get('content'),
            updatedAt: new Date().toISOString()
        };

        if (currentEditId) {
            // Update existing prompt
            devPrompts[currentEditId] = {
                ...devPrompts[currentEditId],
                ...promptData
            };
        } else {
            // Create new prompt
            const id = generateId();
            devPrompts[id] = {
                id: id,
                ...promptData,
                createdAt: new Date().toISOString()
            };
        }

        saveDevPrompts();
        renderDevPrompts();
        hideModal(editModal);
    });

    // Action button event listeners
    document.getElementById('add-prompt-btn').addEventListener('click', addNewPrompt);

    document.getElementById('export-btn').addEventListener('click', function() {
        const dataStr = JSON.stringify(devPrompts, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'dev-prompts-' + new Date().toISOString().split('T')[0] + '.json';
        link.click();
        URL.revokeObjectURL(url);
    });

    document.getElementById('import-btn').addEventListener('click', function() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const importedData = JSON.parse(e.target.result);
                        if (confirm('匯入資料將會覆蓋現有的提示詞，確定要繼續嗎？')) {
                            devPrompts = importedData;
                            saveDevPrompts();
                            renderDevPrompts();
                            alert('匯入成功！');
                        }
                    } catch (error) {
                        alert('匯入失敗：檔案格式不正確');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    });

    // Search and filter event listeners
    document.getElementById('search-input').addEventListener('input', renderDevPrompts);
    document.getElementById('category-filter').addEventListener('change', renderDevPrompts);

    // Initialize
    createDefaultPrompts();
    renderDevPrompts();

    // Smooth scroll for navigation links (legacy support)
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
            if (!mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden'); // Close mobile menu after click
            }
        });
    });

});
</script>
</body>
</html>
