/* 智能提示詞管理平台 - 樣式文件 */
/* Chosen Palette: Scholarly Warmth */

@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;500;700&display=swap');

/* 基礎樣式 */
body {
    font-family: 'Noto Sans TC', sans-serif;
    background-color: #F8F7F4; /* Light Cream */
    color: #333333; /* Dark Grey */
}

/* 色彩系統 */
.bg-primary { background-color: #F8F7F4; }
.bg-secondary { background-color: #FFFFFF; }
.text-accent { color: #C87E6A; } /* Warm Terracotta */
.border-accent { border-color: #C87E6A; }
.bg-accent { background-color: #C87E6A; }
.bg-accent-light { background-color: #DDC3B8; } /* Lighter Terracotta */

/* 導航樣式 */
.nav-link {
    transition: color 0.3s ease, border-bottom-color 0.3s ease;
    border-bottom: 2px solid transparent;
}
.nav-link:hover, .nav-link.active {
    color: #C87E6A;
    border-bottom-color: #C87E6A;
}

/* 模態框樣式 */
.modal {
    transition: opacity 0.3s ease-in-out;
}
.modal-content {
    transition: transform 0.3s ease-in-out;
}

/* 圖表容器 */
.chart-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    height: 300px;
    max-height: 400px;
}
@media (min-width: 768px) {
    .chart-container {
        height: 350px;
    }
}

/* 知識圖譜樣式 */
.node {
    background-color: #DDC3B8;
    border: 1px solid #C87E6A;
    border-radius: 8px;
    padding: 8px 12px;
    text-align: center;
    font-weight: 500;
    position: relative;
    z-index: 10;
}
.node.core {
    font-weight: bold;
    background-color: #C87E6A;
    color: white;
    padding: 12px 18px;
    border-radius: 12px;
}

/* 分頁系統樣式 */
.tab-button {
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}
.tab-button.active {
    color: #C87E6A;
    border-bottom-color: #C87E6A;
    background-color: rgba(200, 126, 106, 0.1);
}
.tab-button:hover:not(.active) {
    color: #C87E6A;
    background-color: rgba(200, 126, 106, 0.05);
}
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
}

/* 編輯表單樣式 */
.edit-form input, .edit-form textarea {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}
.edit-form input:focus, .edit-form textarea:focus {
    border-color: #C87E6A;
    box-shadow: 0 0 0 3px rgba(200, 126, 106, 0.1);
    outline: none;
}

/* 按鈕樣式 */
.btn-primary {
    background-color: #C87E6A;
    color: white;
    transition: background-color 0.3s ease;
}
.btn-primary:hover {
    background-color: #B86B57;
}
.btn-secondary {
    background-color: #DDC3B8;
    color: #333333;
    transition: background-color 0.3s ease;
}
.btn-secondary:hover {
    background-color: #D0B5A8;
}
.btn-danger {
    background-color: #DC3545;
    color: white;
    transition: background-color 0.3s ease;
}
.btn-danger:hover {
    background-color: #C82333;
}
